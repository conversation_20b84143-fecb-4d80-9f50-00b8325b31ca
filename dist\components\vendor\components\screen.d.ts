/**
 * Enhanced Screen Component
 *
 * Wrapper around blessed.screen with additional functionality
 * for better integration and management.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export interface ScreenOptions extends blessed.Widgets.IScreenOptions {
    onResize?: (width: number, height: number) => void;
    onKeypress?: (ch: string, key: blessed.Widgets.Events.IKeyEventArg) => void;
    onExit?: () => void;
}
export declare class EnhancedScreen extends EventEmitter {
    private screen;
    private options;
    private isDestroyed;
    constructor(options?: ScreenOptions);
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Setup global key handlers
     */
    private setupGlobalKeyHandlers;
    /**
     * Get the underlying blessed screen
     */
    getScreen(): blessed.Widgets.Screen;
    /**
     * Render the screen
     */
    render(): void;
    /**
     * Clear the screen
     */
    clear(): void;
    /**
     * Focus an element
     */
    focus(element: blessed.Widgets.Node): void;
    /**
     * Get screen dimensions
     */
    getDimensions(): {
        width: number;
        height: number;
    };
    /**
     * Set screen title
     */
    setTitle(title: string): void;
    /**
     * Toggle debug mode
     */
    private toggleDebug;
    /**
     * Add global key handler
     */
    key(keys: string | string[], callback: (ch?: string, key?: blessed.Widgets.Events.IKeyEventArg) => void): void;
    /**
     * Remove key handler
     */
    unkey(keys: string | string[], callback?: Function): void;
    /**
     * Save screen state
     */
    saveState(): void;
    /**
     * Restore screen state
     */
    restoreState(): void;
    /**
     * Take screenshot (debug feature)
     */
    screenshot(): string;
    /**
     * Exit the application
     */
    exit(code?: number): void;
    /**
     * Destroy the screen
     */
    destroy(): void;
    /**
     * Check if screen is destroyed
     */
    get destroyed(): boolean;
    /**
     * Get screen width
     */
    get width(): number;
    /**
     * Get screen height
     */
    get height(): number;
    /**
     * Get screen title
     */
    get title(): string;
    /**
     * Proxy method calls to underlying screen
     */
    append(element: blessed.Widgets.Node): void;
    remove(element: blessed.Widgets.Node): void;
    insert(element: blessed.Widgets.Node, index: number): void;
    insertBefore(element: blessed.Widgets.Node, refElement: blessed.Widgets.Node): void;
    insertAfter(element: blessed.Widgets.Node, refElement: blessed.Widgets.Node): void;
}
export default EnhancedScreen;
//# sourceMappingURL=screen.d.ts.map