/**
 * Multi-Platform Sandbox System
 * 
 * Provides secure command execution with platform-specific isolation,
 * resource limits, and safety controls.
 */

import { platform } from 'os';
import { WindowsSandbox } from './windows-sandbox.js';
import { UnixSandbox } from './unix-sandbox.js';
import { logDebug, logError, logWarn, toError } from '../../logger/log.js';
import type { ExecInput, ExecResult } from '../../../types/index.js';

export interface SandboxOptions {
  workingDirectory?: string;
  timeout?: number;
  maxMemory?: number;
  maxCpuTime?: number;
  allowNetworking?: boolean;
  allowFileSystem?: boolean;
  restrictedPaths?: string[];
  environmentVariables?: Record<string, string>;
  uid?: number;
  gid?: number;
}

export interface SandboxCapabilities {
  isolation: boolean;
  resourceLimits: boolean;
  networkRestriction: boolean;
  fileSystemRestriction: boolean;
  userIsolation: boolean;
  processLimits: boolean;
}

export abstract class BaseSandbox {
  protected options: Required<SandboxOptions>;

  constructor(options: SandboxOptions = {}) {
    this.options = {
      workingDirectory: options.workingDirectory || process.cwd(),
      timeout: options.timeout || 30000,
      maxMemory: options.maxMemory || 512 * 1024 * 1024, // 512MB
      maxCpuTime: options.maxCpuTime || 10000, // 10 seconds
      allowNetworking: options.allowNetworking ?? false,
      allowFileSystem: options.allowFileSystem ?? true,
      restrictedPaths: options.restrictedPaths || [],
      environmentVariables: options.environmentVariables || {},
      uid: options.uid || process.getuid?.() || 0,
      gid: options.gid || process.getgid?.() || 0,
    };
  }

  /**
   * Execute command in sandbox
   */
  abstract execute(input: ExecInput): Promise<ExecResult>;

  /**
   * Get sandbox capabilities
   */
  abstract getCapabilities(): SandboxCapabilities;

  /**
   * Check if sandbox is available
   */
  abstract isAvailable(): Promise<boolean>;

  /**
   * Setup sandbox environment
   */
  abstract setup(): Promise<void>;

  /**
   * Cleanup sandbox resources
   */
  abstract cleanup(): Promise<void>;

  /**
   * Validate command before execution
   */
  protected validateCommand(input: ExecInput): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check command format
    if (!input.command || input.command.length === 0) {
      errors.push('Command cannot be empty');
    }

    // Check for dangerous commands
    const dangerousCommands = [
      'rm', 'rmdir', 'del', 'format', 'fdisk',
      'sudo', 'su', 'chmod', 'chown',
      'kill', 'killall', 'shutdown', 'reboot',
    ];

    const baseCommand = input.command[0]?.toLowerCase();
    if (baseCommand && dangerousCommands.includes(baseCommand)) {
      errors.push(`Potentially dangerous command: ${baseCommand}`);
    }

    // Check working directory
    if (input.workdir && this.options.restrictedPaths.length > 0) {
      const isRestricted = this.options.restrictedPaths.some(path => 
        input.workdir!.startsWith(path)
      );
      
      if (isRestricted) {
        errors.push(`Access to restricted path: ${input.workdir}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Apply resource limits to command
   */
  protected applyResourceLimits(command: string[]): string[] {
    const platform = process.platform;
    
    if (platform === 'win32') {
      // Windows doesn't have built-in resource limiting like Unix
      return command;
    }

    // Unix-like systems can use ulimit or timeout
    const limitedCommand = ['timeout', `${this.options.timeout / 1000}s`];
    
    // Add memory limit if supported
    if (this.options.maxMemory > 0) {
      limitedCommand.push('--preserve-status');
    }
    
    limitedCommand.push(...command);
    return limitedCommand;
  }

  /**
   * Sanitize environment variables
   */
  protected sanitizeEnvironment(): Record<string, string> {
    const env = { ...process.env };
    
    // Remove potentially dangerous environment variables
    const dangerousVars = [
      'LD_PRELOAD', 'LD_LIBRARY_PATH', 'DYLD_INSERT_LIBRARIES',
      'PYTHONPATH', 'NODE_PATH', 'RUBYLIB',
    ];
    
    for (const varName of dangerousVars) {
      delete env[varName];
    }
    
    // Add custom environment variables
    Object.assign(env, this.options.environmentVariables);
    
    return env;
  }

  /**
   * Log sandbox activity
   */
  protected logActivity(action: string, details: any): void {
    logDebug(`Sandbox ${action}:`, details);
  }
}

/**
 * Create platform-appropriate sandbox
 */
export function createSandbox(options: SandboxOptions = {}): BaseSandbox {
  const currentPlatform = platform();
  
  switch (currentPlatform) {
    case 'win32':
      return new WindowsSandbox(options);
    case 'linux':
    case 'darwin':
    case 'freebsd':
    case 'openbsd':
      return new UnixSandbox(options);
    default:
      logWarn(`Unsupported platform for sandboxing: ${currentPlatform}`);
      return new UnixSandbox(options); // Fallback to Unix sandbox
  }
}

/**
 * Check if sandboxing is supported on current platform
 */
export async function isSandboxingSupported(): Promise<boolean> {
  try {
    const sandbox = createSandbox();
    return await sandbox.isAvailable();
  } catch (error) {
    const err = toError(error);
    logError('Error checking sandbox support', err);
    return false;
  }
}

/**
 * Get sandbox capabilities for current platform
 */
export function getSandboxCapabilities(): SandboxCapabilities {
  const sandbox = createSandbox();
  return sandbox.getCapabilities();
}

/**
 * Execute command with automatic sandbox selection
 */
export async function executeInSandbox(
  input: ExecInput,
  options: SandboxOptions = {}
): Promise<ExecResult> {
  const sandbox = createSandbox(options);
  
  try {
    await sandbox.setup();
    const result = await sandbox.execute(input);
    await sandbox.cleanup();
    return result;
  } catch (error) {
    await sandbox.cleanup();
    throw error;
  }
}

/**
 * Test sandbox functionality
 */
export async function testSandbox(options: SandboxOptions = {}): Promise<{
  supported: boolean;
  capabilities: SandboxCapabilities;
  testResults: Array<{ test: string; passed: boolean; error?: string }>;
}> {
  const sandbox = createSandbox(options);
  const capabilities = sandbox.getCapabilities();
  const testResults: Array<{ test: string; passed: boolean; error?: string }> = [];
  
  // Test basic execution
  try {
    await sandbox.setup();
    
    const basicTest = await sandbox.execute({
      command: ['echo', 'test'],
    });
    
    testResults.push({
      test: 'Basic command execution',
      passed: basicTest.exitCode === 0 && basicTest.stdout.includes('test'),
    });
  } catch (error) {
    const err = toError(error);
    testResults.push({
      test: 'Basic command execution',
      passed: false,
      error: err.message,
    });
  }
  
  // Test timeout handling
  try {
    const timeoutTest = await sandbox.execute({
      command: ['sleep', '2'],
    });
    
    testResults.push({
      test: 'Timeout handling',
      passed: timeoutTest.exitCode !== 0, // Should timeout
    });
  } catch (error) {
    testResults.push({
      test: 'Timeout handling',
      passed: true, // Timeout is expected
    });
  }
  
  await sandbox.cleanup();
  
  return {
    supported: await sandbox.isAvailable(),
    capabilities,
    testResults,
  };
}

export default {
  createSandbox,
  executeInSandbox,
  isSandboxingSupported,
  getSandboxCapabilities,
  testSandbox,
  BaseSandbox,
};
