/**
 * Package Manager Detection
 *
 * Detects which package manager was used to install the CLI
 * and provides appropriate update commands.
 */
import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { dirname, join } from 'path';
/**
 * Detect package manager by installation path
 */
export async function detectInstallerByPath() {
    try {
        // Get the path of the current executable
        const execPath = process.argv[0];
        const binPath = process.argv[1];
        // Check various indicators
        // 1. Check if installed via pnpm
        if (binPath.includes('pnpm') || binPath.includes('.pnpm')) {
            return 'pnpm';
        }
        // 2. Check if installed via yarn
        if (binPath.includes('yarn') || binPath.includes('.yarn')) {
            return 'yarn';
        }
        // 3. Check if installed via bun
        if (binPath.includes('bun') || execPath.includes('bun')) {
            return 'bun';
        }
        // 4. Check if running via deno
        if (execPath.includes('deno') || process.env.DENO_VERSION) {
            return 'deno';
        }
        // 5. Check for package manager lock files in parent directories
        const lockFileAgent = detectByLockFiles();
        if (lockFileAgent) {
            return lockFileAgent;
        }
        // 6. Check which package managers are available
        const availableAgent = await detectAvailableAgent();
        if (availableAgent) {
            return availableAgent;
        }
        // 7. Default to npm
        return 'npm';
    }
    catch (error) {
        // Fallback to npm on any error
        return 'npm';
    }
}
/**
 * Detect package manager by lock files
 */
function detectByLockFiles() {
    let currentDir = process.cwd();
    const maxDepth = 5;
    let depth = 0;
    while (depth < maxDepth) {
        // Check for lock files
        if (existsSync(join(currentDir, 'pnpm-lock.yaml'))) {
            return 'pnpm';
        }
        if (existsSync(join(currentDir, 'yarn.lock'))) {
            return 'yarn';
        }
        if (existsSync(join(currentDir, 'bun.lockb'))) {
            return 'bun';
        }
        if (existsSync(join(currentDir, 'deno.lock'))) {
            return 'deno';
        }
        // Move up one directory
        const parentDir = dirname(currentDir);
        if (parentDir === currentDir) {
            break; // Reached root
        }
        currentDir = parentDir;
        depth++;
    }
    return undefined;
}
/**
 * Detect available package manager
 */
async function detectAvailableAgent() {
    const agents = ['pnpm', 'yarn', 'bun', 'npm', 'deno'];
    for (const agent of agents) {
        if (await isAgentAvailable(agent)) {
            return agent;
        }
    }
    return undefined;
}
/**
 * Check if package manager is available
 */
async function isAgentAvailable(agent) {
    try {
        const command = agent === 'deno' ? 'deno --version' : `${agent} --version`;
        execSync(command, {
            stdio: 'pipe',
            timeout: 5000,
        });
        return true;
    }
    catch (error) {
        return false;
    }
}
/**
 * Get install command for package manager
 */
export function getInstallCommand(agent, packageName, global = true) {
    switch (agent) {
        case 'npm':
            return global ? `npm install -g ${packageName}` : `npm install ${packageName}`;
        case 'pnpm':
            return global ? `pnpm add -g ${packageName}` : `pnpm add ${packageName}`;
        case 'yarn':
            return global ? `yarn global add ${packageName}` : `yarn add ${packageName}`;
        case 'bun':
            return global ? `bun add -g ${packageName}` : `bun add ${packageName}`;
        case 'deno':
            return `deno install -A -f -n ${packageName.replace('@', '').replace('/', '_')} https://deno.land/x/${packageName}/mod.ts`;
        default:
            return `npm install -g ${packageName}`;
    }
}
/**
 * Get update command for package manager
 */
export function getUpdateCommand(agent, packageName, global = true) {
    switch (agent) {
        case 'npm':
            return global ? `npm update -g ${packageName}` : `npm update ${packageName}`;
        case 'pnpm':
            return global ? `pnpm update -g ${packageName}` : `pnpm update ${packageName}`;
        case 'yarn':
            return global ? `yarn global upgrade ${packageName}` : `yarn upgrade ${packageName}`;
        case 'bun':
            return global ? `bun update -g ${packageName}` : `bun update ${packageName}`;
        case 'deno':
            return `deno install -A -f -n ${packageName.replace('@', '').replace('/', '_')} https://deno.land/x/${packageName}/mod.ts`;
        default:
            return `npm update -g ${packageName}`;
    }
}
/**
 * Get uninstall command for package manager
 */
export function getUninstallCommand(agent, packageName, global = true) {
    switch (agent) {
        case 'npm':
            return global ? `npm uninstall -g ${packageName}` : `npm uninstall ${packageName}`;
        case 'pnpm':
            return global ? `pnpm remove -g ${packageName}` : `pnpm remove ${packageName}`;
        case 'yarn':
            return global ? `yarn global remove ${packageName}` : `yarn remove ${packageName}`;
        case 'bun':
            return global ? `bun remove -g ${packageName}` : `bun remove ${packageName}`;
        case 'deno':
            return `deno uninstall ${packageName.replace('@', '').replace('/', '_')}`;
        default:
            return `npm uninstall -g ${packageName}`;
    }
}
/**
 * Get package manager version
 */
export async function getAgentVersion(agent) {
    try {
        const command = agent === 'deno' ? 'deno --version' : `${agent} --version`;
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: 'pipe',
            timeout: 5000,
        });
        // Extract version number from output
        const versionMatch = result.match(/(\d+\.\d+\.\d+)/);
        return versionMatch ? versionMatch[1] : result.trim();
    }
    catch (error) {
        return null;
    }
}
/**
 * Get all available package managers
 */
export async function getAvailableAgents() {
    const agents = ['npm', 'pnpm', 'yarn', 'bun', 'deno'];
    const results = [];
    for (const agent of agents) {
        const available = await isAgentAvailable(agent);
        const version = available ? await getAgentVersion(agent) : null;
        results.push({
            name: agent,
            version,
            available,
        });
    }
    return results;
}
/**
 * Detect package manager from package.json scripts
 */
export function detectFromPackageJson(packageJsonPath) {
    try {
        const fs = require('fs');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        // Check packageManager field
        if (packageJson.packageManager) {
            const pm = packageJson.packageManager.toLowerCase();
            if (pm.includes('pnpm'))
                return 'pnpm';
            if (pm.includes('yarn'))
                return 'yarn';
            if (pm.includes('bun'))
                return 'bun';
            if (pm.includes('npm'))
                return 'npm';
        }
        // Check scripts for package manager hints
        const scripts = packageJson.scripts || {};
        const scriptValues = Object.values(scripts).join(' ');
        if (scriptValues.includes('pnpm'))
            return 'pnpm';
        if (scriptValues.includes('yarn'))
            return 'yarn';
        if (scriptValues.includes('bun'))
            return 'bun';
        return undefined;
    }
    catch (error) {
        return undefined;
    }
}
/**
 * Get recommended package manager
 */
export async function getRecommendedAgent() {
    // Try to detect current agent
    const detected = await detectInstallerByPath();
    if (detected) {
        return detected;
    }
    // Check availability in order of preference
    const preferenceOrder = ['pnpm', 'bun', 'yarn', 'npm'];
    for (const agent of preferenceOrder) {
        if (await isAgentAvailable(agent)) {
            return agent;
        }
    }
    // Fallback to npm
    return 'npm';
}
//# sourceMappingURL=package-manager-detector.js.map