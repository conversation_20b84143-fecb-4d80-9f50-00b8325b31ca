/**
 * Response Processing System
 *
 * Handles real-time streaming responses from AI providers,
 * processes function calls, and manages conversation state.
 */
import { nanoid } from 'nanoid';
import { toError } from './logger/log.js';
/**
 * Stream responses from OpenAI-compatible API
 */
export async function* streamResponses(input, completion) {
    const responseId = nanoid();
    const startTime = Date.now();
    let currentContent = '';
    let currentFunctionCall = null;
    let functionCalls = [];
    let usage = null;
    // Emit response created event
    yield {
        type: 'response.created',
        data: {
            id: responseId,
            model: input.model,
            provider: input.provider,
        },
        timestamp: Date.now(),
    };
    try {
        for await (const chunk of completion) {
            const choice = chunk.choices?.[0];
            if (!choice)
                continue;
            const delta = choice.delta;
            // Handle content delta
            if (delta.content) {
                currentContent += delta.content;
                yield {
                    type: 'response.output_text.delta',
                    data: {
                        delta: delta.content,
                        content: currentContent,
                    },
                    timestamp: Date.now(),
                };
            }
            // Handle function call deltas
            if (delta.tool_calls) {
                for (const toolCall of delta.tool_calls) {
                    if (toolCall.function) {
                        if (!currentFunctionCall || toolCall.index !== undefined) {
                            // Start new function call
                            currentFunctionCall = {
                                id: toolCall.id || nanoid(),
                                name: toolCall.function.name || '',
                                arguments: toolCall.function.arguments || '',
                            };
                        }
                        else {
                            // Continue existing function call
                            if (toolCall.function.name) {
                                currentFunctionCall.name += toolCall.function.name;
                            }
                            if (toolCall.function.arguments) {
                                currentFunctionCall.arguments += toolCall.function.arguments;
                            }
                        }
                        yield {
                            type: 'response.function_call_arguments.delta',
                            data: {
                                functionCall: currentFunctionCall,
                                delta: toolCall.function.arguments || '',
                            },
                            timestamp: Date.now(),
                        };
                    }
                }
            }
            // Handle function call completion
            if (choice.finish_reason === 'tool_calls' && currentFunctionCall) {
                const completedCall = {
                    id: currentFunctionCall.id || nanoid(),
                    name: currentFunctionCall.name || '',
                    arguments: currentFunctionCall.arguments || '',
                };
                functionCalls.push(completedCall);
                yield {
                    type: 'response.function_call_arguments.done',
                    data: {
                        functionCall: completedCall,
                    },
                    timestamp: Date.now(),
                };
                currentFunctionCall = null;
            }
            // Handle usage information
            if (chunk.usage) {
                usage = chunk.usage;
            }
            // Handle completion
            if (choice.finish_reason && choice.finish_reason !== 'tool_calls') {
                break;
            }
        }
        // Create final response content
        const content = [];
        if (currentContent.trim()) {
            content.push({
                type: 'output_text',
                text: currentContent,
            });
        }
        for (const functionCall of functionCalls) {
            content.push({
                type: 'function_call',
                functionCall,
            });
        }
        // Create final response item
        const responseItem = {
            role: 'assistant',
            content,
            type: 'message',
            id: responseId,
            timestamp: startTime,
            model: input.model,
            usage: usage ? {
                prompt_tokens: usage.prompt_tokens || 0,
                completion_tokens: usage.completion_tokens || 0,
                total_tokens: usage.total_tokens || 0,
            } : undefined,
        };
        yield {
            type: 'response.completed',
            data: {
                response: responseItem,
                duration: Date.now() - startTime,
            },
            timestamp: Date.now(),
        };
    }
    catch (error) {
        const err = toError(error);
        yield {
            type: 'response.error',
            data: {
                error: err.message,
                code: error?.code || 'UNKNOWN_ERROR',
            },
            timestamp: Date.now(),
        };
    }
}
/**
 * Create non-streaming response
 */
export async function createResponse(input, client) {
    const responseId = nanoid();
    const startTime = Date.now();
    try {
        const completion = await client.chat.completions.create({
            model: input.model,
            messages: input.messages.map(msg => {
                const baseMessage = {
                    role: msg.role,
                    content: msg.content.map(c => {
                        if (c.type === 'input_text') {
                            return { type: 'text', text: c.text || '' };
                        }
                        else if (c.type === 'input_image' && c.image) {
                            return {
                                type: 'image_url',
                                image_url: {
                                    url: c.image.data || c.image.url || '',
                                },
                            };
                        }
                        return { type: 'text', text: '' };
                    }),
                };
                // Add tool_call_id for tool messages
                if (msg.role === 'tool' && msg.id) {
                    baseMessage.tool_call_id = msg.id;
                }
                return baseMessage;
            }),
            max_tokens: input.maxTokens,
            temperature: input.temperature,
            tools: input.tools,
            stream: false,
        });
        const choice = completion.choices[0];
        const content = [];
        // Add text content
        if (choice.message.content) {
            content.push({
                type: 'output_text',
                text: choice.message.content,
            });
        }
        // Add function calls
        if (choice.message.tool_calls) {
            for (const toolCall of choice.message.tool_calls) {
                if (toolCall.type === 'function') {
                    content.push({
                        type: 'function_call',
                        functionCall: {
                            id: toolCall.id,
                            name: toolCall.function.name,
                            arguments: toolCall.function.arguments,
                        },
                    });
                }
            }
        }
        return {
            role: 'assistant',
            content,
            type: 'message',
            id: responseId,
            timestamp: startTime,
            model: input.model,
            usage: completion.usage ? {
                prompt_tokens: completion.usage.prompt_tokens,
                completion_tokens: completion.usage.completion_tokens,
                total_tokens: completion.usage.total_tokens,
            } : undefined,
        };
    }
    catch (error) {
        const err = toError(error);
        throw new Error(`Failed to create response: ${err.message}`);
    }
}
/**
 * Convert OpenAI messages to internal format
 */
export function convertToInternalMessages(messages) {
    return messages.map(msg => ({
        role: msg.role,
        content: typeof msg.content === 'string'
            ? [{ type: 'input_text', text: msg.content }]
            : msg.content.map(c => {
                if (c.type === 'text') {
                    return { type: 'input_text', text: c.text };
                }
                else if (c.type === 'image_url') {
                    return {
                        type: 'input_image',
                        image: { url: c.image_url.url },
                    };
                }
                return { type: 'input_text', text: '' };
            }),
        type: 'message',
        timestamp: Date.now(),
    }));
}
/**
 * Convert internal messages to OpenAI format
 */
export function convertToOpenAIMessages(messages) {
    return messages.map(msg => {
        if (msg.content.length === 1 && msg.content[0].type === 'input_text') {
            return {
                role: msg.role,
                content: msg.content[0].text || '',
            };
        }
        return {
            role: msg.role,
            content: msg.content.map(c => {
                if (c.type === 'input_text') {
                    return { type: 'text', text: c.text || '' };
                }
                else if (c.type === 'input_image' && c.image) {
                    return {
                        type: 'image_url',
                        image_url: {
                            url: c.image.data || c.image.url || '',
                        },
                    };
                }
                return { type: 'text', text: '' };
            }),
        };
    });
}
/**
 * Extract function calls from response
 */
export function extractFunctionCalls(response) {
    return response.content
        .filter(c => c.type === 'function_call' && c.functionCall)
        .map(c => c.functionCall);
}
/**
 * Extract text content from response
 */
export function extractTextContent(response) {
    return response.content
        .filter(c => c.type === 'output_text' && c.text)
        .map(c => c.text)
        .join('\n');
}
/**
 * Calculate response statistics
 */
export function calculateResponseStats(response) {
    const textContent = extractTextContent(response);
    const functionCalls = extractFunctionCalls(response);
    return {
        textLength: textContent.length,
        functionCalls: functionCalls.length,
        estimatedTokens: Math.ceil(textContent.length / 4),
        duration: response.usage ? undefined : Date.now() - response.timestamp,
    };
}
//# sourceMappingURL=responses.js.map