{"version": 3, "file": "agent-loop.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/agent-loop.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAClE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AACrF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAc7D;;GAEG;AACH,MAAM,iBAAiB,GAAiB;IACtC,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,kIAAkI;IAC/I,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACzB,WAAW,EAAE,mEAAmE;aACjF;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8CAA8C;aAC5D;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oDAAoD;aAClE;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAiB;IAC3C,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,aAAa;IACnB,WAAW,EAAE,yGAAyG;IACtH,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACzB,WAAW,EAAE,2CAA2C;aACzD;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8CAA8C;aAC5D;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,YAAY;IACjC,KAAK,CAAS;IACd,QAAQ,CAAS;IACjB,cAAc,CAAiB;IAC/B,MAAM,CAAM;IACZ,UAAU,GAAwB,EAAE,CAAC;IACrC,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;IAClC,oBAAoB,GAAG,CAAC,CAAC;IACzB,aAAa,CAAS;IACtB,OAAO,CAAS;IAChB,KAAK,CAAU;IAEvB,YAAY,MAAuB;QACjC,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;QAEnC,uBAAuB;QACvB,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;QAEH,OAAO,CAAC,2BAA2B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,KAAwB;QAChD,MAAM,KAAK,GAAG,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,OAAO,CAAC,0CAA0C,CAAC,CAAC;YAEpD,0BAA0B;YAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5B,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEjD,+CAA+C;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEvC,oBAAoB;YACpB,MAAM,YAAY,GAAG;gBACnB,QAAQ;gBACR,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,GAAG;gBAChB,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC3C,IAAI,EAAE,UAAmB;oBACzB,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;qBAC5B;iBACF,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACf,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,oCAAoC;YACpC,IAAI,QAA4B,CAAC;YAEjC,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;gBACxB,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,MAAM,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7D,CAAC;YAED,mCAAmC;YACnC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAEvC,KAAK,CAAC,GAAG,EAAE,CAAC;YACZ,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,GAAG,EAAE,CAAC;YACZ,QAAQ,CAAC,8BAA8B,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,YAAiB;QACtD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,GAAG,YAAY;gBACf,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,aAAa,GAA8B,IAAI,CAAC;YAEpD,2BAA2B;YAC3B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,eAAe,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBAEnC,IAAI,KAAK,CAAC,IAAI,KAAK,oBAAoB,EAAE,CAAC;oBACxC,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACpC,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnD,CAAC;YACH,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,sCAAsC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC5B,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;gBAC9C,CAAC;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;oBAC/C,OAAO;wBACL,IAAI,EAAE,WAAW;wBACjB,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,EAAE;qBACtD,CAAC;gBACJ,CAAC;gBACD,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YACpC,CAAC,CAAC;SACH,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,KAAK,GAAmB,EAAE,CAAC;QAEjC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAA4B;QACnD,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,QAA4B;QAC7D,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;QAEjG,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,CAAC,cAAc,aAAa,CAAC,MAAM,iBAAiB,CAAC,CAAC;QAE7D,6BAA6B;QAC7B,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE,CAAC;YACxC,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,YAAsC;QACrE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;QAErD,IAAI,CAAC;YACH,QAAQ,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;YAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEpC,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,OAAO,CAAC;gBACb,KAAK,aAAa;oBAChB,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACpC,MAAM;gBACR;oBACE,QAAQ,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,yBAAyB,IAAI,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,IAAS;QACxC,IAAI,CAAC;YACH,MAAM,SAAS,GAAc;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;gBACtC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;aAC/B,CAAC;YAEF,kCAAkC;YAClC,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,SAAS,EAAE;gBAChD,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,gBAAgB,EAAE,SAAS,CAAC,OAAO;aACpC,CAAC,CAAC;YAEH,OAAO,CAAC,2BAA2B,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClE,QAAQ,CAAC,mBAAmB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,gCAAgC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,QAA4B;QAC1D,uDAAuD;QACvD,MAAM,cAAc,GAAsB;YACxC,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAChC,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBAC7B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;gBACpD,CAAC;gBACD,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC1C,CAAC,CAAC;YACF,IAAI,EAAE,SAAS;YACf,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,SAAS,EAAE,QAAQ,CAAC,SAAS;SAC9B,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAa,EAAE,QAAiB;QACjD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;gBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,kBAAkB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAsB;QAChD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,OAAO,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAgC;QAClD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC5B,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAChC,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;gBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAC5C,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,qCAAqC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,OAAO,CAAC,iCAAiC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,QAAQ;QAKb,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACpC,YAAY,EAAE,IAAI,CAAC,oBAAoB;YACvC,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACV,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;CACF"}