/**
 * File Operations Processor
 *
 * Processes AI responses containing file operations and applies them
 * to the filesystem with proper validation and error handling.
 */
import { writeFileSync, existsSync, mkdirSync, unlinkSync, renameSync } from 'fs';
import { dirname, resolve } from 'path';
import { logInfo, logError, logDebug } from '../logger/log.js';
/**
 * Process file operations from AI response
 */
export async function processFileOperations(response, rootPath) {
    const result = {
        success: false,
        filesModified: [],
        filesCreated: [],
        filesDeleted: [],
        errors: [],
        warnings: [],
    };
    try {
        logInfo('Processing file operations from AI response');
        // Extract file operations from response
        const operations = extractFileOperations(response);
        if (operations.length === 0) {
            result.warnings.push('No file operations found in response');
            result.success = true;
            return result;
        }
        logDebug(`Found ${operations.length} file operations`);
        // Validate operations
        const validationErrors = validateOperations(operations, rootPath);
        if (validationErrors.length > 0) {
            result.errors.push(...validationErrors);
            return result;
        }
        // Apply operations
        for (const operation of operations) {
            try {
                await applyFileOperation(operation, rootPath, result);
            }
            catch (error) {
                result.errors.push(`Failed to apply operation ${operation.type} on ${operation.path}: ${error.message}`);
            }
        }
        result.success = result.errors.length === 0;
        if (result.success) {
            logInfo(`File operations completed successfully: ${operations.length} operations`);
        }
        else {
            logError(`File operations failed: ${result.errors.length} errors`);
        }
        return result;
    }
    catch (error) {
        logError('File operations processing failed', error);
        result.errors.push(`Processing failed: ${error.message}`);
        return result;
    }
}
/**
 * Extract file operations from AI response text
 */
function extractFileOperations(response) {
    const operations = [];
    // Pattern 1: File creation blocks
    const createPattern = /(?:create|new)\s+file[:\s]+([^\n]+)\n```(?:\w+)?\n([\s\S]*?)\n```/gi;
    let match;
    while ((match = createPattern.exec(response)) !== null) {
        operations.push({
            type: 'create',
            path: match[1].trim(),
            content: match[2],
        });
    }
    // Pattern 2: File update blocks
    const updatePattern = /(?:update|modify)\s+file[:\s]+([^\n]+)\n```(?:\w+)?\n([\s\S]*?)\n```/gi;
    while ((match = updatePattern.exec(response)) !== null) {
        operations.push({
            type: 'update',
            path: match[1].trim(),
            content: match[2],
        });
    }
    // Pattern 3: File deletion
    const deletePattern = /(?:delete|remove)\s+file[:\s]+([^\n]+)/gi;
    while ((match = deletePattern.exec(response)) !== null) {
        operations.push({
            type: 'delete',
            path: match[1].trim(),
        });
    }
    // Pattern 4: File move/rename
    const movePattern = /(?:move|rename)\s+file[:\s]+([^\n]+)\s+(?:to|->)\s+([^\n]+)/gi;
    while ((match = movePattern.exec(response)) !== null) {
        operations.push({
            type: 'move',
            path: match[1].trim(),
            newPath: match[2].trim(),
        });
    }
    // Pattern 5: Structured format
    const structuredPattern = /FILE_OPERATION:\s*(\w+)\s*\nPATH:\s*([^\n]+)(?:\nNEW_PATH:\s*([^\n]+))?(?:\nCONTENT:\s*\n```(?:\w+)?\n([\s\S]*?)\n```)?/gi;
    while ((match = structuredPattern.exec(response)) !== null) {
        const operation = {
            type: match[1].toLowerCase(),
            path: match[2].trim(),
        };
        if (match[3]) {
            operation.newPath = match[3].trim();
        }
        if (match[4]) {
            operation.content = match[4];
        }
        operations.push(operation);
    }
    return operations;
}
/**
 * Validate file operations before applying
 */
function validateOperations(operations, rootPath) {
    const errors = [];
    for (const operation of operations) {
        const fullPath = resolve(rootPath, operation.path);
        // Check if path is within root directory
        if (!fullPath.startsWith(resolve(rootPath))) {
            errors.push(`Path outside root directory: ${operation.path}`);
            continue;
        }
        // Validate based on operation type
        switch (operation.type) {
            case 'create':
                if (existsSync(fullPath)) {
                    errors.push(`File already exists: ${operation.path}`);
                }
                if (!operation.content && operation.content !== '') {
                    errors.push(`Create operation missing content: ${operation.path}`);
                }
                break;
            case 'update':
                if (!existsSync(fullPath)) {
                    errors.push(`File does not exist for update: ${operation.path}`);
                }
                if (!operation.content && operation.content !== '') {
                    errors.push(`Update operation missing content: ${operation.path}`);
                }
                break;
            case 'delete':
                if (!existsSync(fullPath)) {
                    errors.push(`File does not exist for deletion: ${operation.path}`);
                }
                break;
            case 'move':
                if (!existsSync(fullPath)) {
                    errors.push(`Source file does not exist for move: ${operation.path}`);
                }
                if (!operation.newPath) {
                    errors.push(`Move operation missing new path: ${operation.path}`);
                }
                else {
                    const newFullPath = resolve(rootPath, operation.newPath);
                    if (!newFullPath.startsWith(resolve(rootPath))) {
                        errors.push(`New path outside root directory: ${operation.newPath}`);
                    }
                    if (existsSync(newFullPath)) {
                        errors.push(`Target file already exists for move: ${operation.newPath}`);
                    }
                }
                break;
            default:
                errors.push(`Unknown operation type: ${operation.type}`);
        }
    }
    return errors;
}
/**
 * Apply single file operation
 */
async function applyFileOperation(operation, rootPath, result) {
    const fullPath = resolve(rootPath, operation.path);
    switch (operation.type) {
        case 'create':
            await createFile(fullPath, operation.content, result);
            break;
        case 'update':
            await updateFile(fullPath, operation.content, result);
            break;
        case 'delete':
            await deleteFile(fullPath, result);
            break;
        case 'move':
            await moveFile(fullPath, resolve(rootPath, operation.newPath), result);
            break;
        default:
            throw new Error(`Unsupported operation type: ${operation.type}`);
    }
}
/**
 * Create new file
 */
async function createFile(filePath, content, result) {
    // Ensure directory exists
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
    }
    writeFileSync(filePath, content, 'utf8');
    result.filesCreated.push(filePath);
    logDebug(`Created file: ${filePath}`);
}
/**
 * Update existing file
 */
async function updateFile(filePath, content, result) {
    writeFileSync(filePath, content, 'utf8');
    result.filesModified.push(filePath);
    logDebug(`Updated file: ${filePath}`);
}
/**
 * Delete file
 */
async function deleteFile(filePath, result) {
    unlinkSync(filePath);
    result.filesDeleted.push(filePath);
    logDebug(`Deleted file: ${filePath}`);
}
/**
 * Move/rename file
 */
async function moveFile(oldPath, newPath, result) {
    // Ensure target directory exists
    const targetDir = dirname(newPath);
    if (!existsSync(targetDir)) {
        mkdirSync(targetDir, { recursive: true });
    }
    renameSync(oldPath, newPath);
    result.filesDeleted.push(oldPath);
    result.filesCreated.push(newPath);
    logDebug(`Moved file: ${oldPath} -> ${newPath}`);
}
/**
 * Extract file content from code blocks
 */
export function extractCodeBlocks(text) {
    const blocks = [];
    // Pattern for code blocks with optional language and filename
    const codeBlockPattern = /```(?:(\w+)(?:\s+([^\n]+))?)?\n([\s\S]*?)\n```/g;
    let match;
    while ((match = codeBlockPattern.exec(text)) !== null) {
        blocks.push({
            language: match[1],
            filename: match[2],
            content: match[3],
        });
    }
    return blocks;
}
/**
 * Detect file operations from natural language
 */
export function detectFileOperationsFromText(text) {
    const operations = [];
    const lines = text.split('\n');
    for (const line of lines) {
        const lowerLine = line.toLowerCase().trim();
        // Create file patterns
        if (lowerLine.includes('create') && (lowerLine.includes('file') || lowerLine.includes('.js') || lowerLine.includes('.ts'))) {
            const pathMatch = line.match(/(?:create|new)\s+(?:file\s+)?([^\s]+\.\w+)/i);
            if (pathMatch) {
                operations.push({
                    type: 'create',
                    path: pathMatch[1],
                    content: '',
                });
            }
        }
        // Delete file patterns
        if (lowerLine.includes('delete') || lowerLine.includes('remove')) {
            const pathMatch = line.match(/(?:delete|remove)\s+(?:file\s+)?([^\s]+\.\w+)/i);
            if (pathMatch) {
                operations.push({
                    type: 'delete',
                    path: pathMatch[1],
                });
            }
        }
        // Move/rename patterns
        if (lowerLine.includes('move') || lowerLine.includes('rename')) {
            const moveMatch = line.match(/(?:move|rename)\s+([^\s]+\.\w+)\s+(?:to|->)\s+([^\s]+\.\w+)/i);
            if (moveMatch) {
                operations.push({
                    type: 'move',
                    path: moveMatch[1],
                    newPath: moveMatch[2],
                });
            }
        }
    }
    return operations;
}
/**
 * Merge file operations to avoid conflicts
 */
export function mergeFileOperations(operations) {
    const merged = [];
    const pathMap = new Map();
    for (const operation of operations) {
        const existing = pathMap.get(operation.path);
        if (!existing) {
            pathMap.set(operation.path, operation);
        }
        else {
            // Merge operations on the same path
            if (existing.type === 'create' && operation.type === 'update') {
                // Create + Update = Create with updated content
                existing.content = operation.content;
            }
            else if (existing.type === 'update' && operation.type === 'update') {
                // Update + Update = Update with latest content
                existing.content = operation.content;
            }
            else if (operation.type === 'delete') {
                // Any operation + Delete = Delete
                existing.type = 'delete';
                delete existing.content;
            }
        }
    }
    return Array.from(pathMap.values());
}
//# sourceMappingURL=file-ops.js.map