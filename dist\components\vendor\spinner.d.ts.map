{"version": 3, "file": "spinner.d.ts", "sourceRoot": "", "sources": ["../../../src/components/vendor/spinner.tsx"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAEtE,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;IAC7B,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACtB,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,YAAY,CAAC;IACrB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,QAAA,MAAM,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAgB1C,CAAC;AAEF,qBAAa,OAAO;IAClB,OAAO,CAAC,OAAO,CAA6B;IAC5C,OAAO,CAAC,MAAM,CAAW;IACzB,OAAO,CAAC,YAAY,CAAK;IACzB,OAAO,CAAC,QAAQ,CAA+B;IAC/C,OAAO,CAAC,KAAK,CAAS;IACtB,OAAO,CAAC,UAAU,CAAS;gBAEf,OAAO,EAAE,cAAc;IAoBnC;;OAEG;IACH,KAAK,IAAI,IAAI;IAkBb;;OAEG;IACH,IAAI,IAAI,IAAI;IAmBZ;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAS3B;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,OAAO,YAAY,GAAG,IAAI;IAa9C;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAS7B;;OAEG;IACH,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU;IAIxC;;OAEG;IACH,QAAQ,IAAI,OAAO;IAInB;;OAEG;IACH,OAAO,IAAI,IAAI;IAKf;;OAEG;IACH,IAAI,IAAI,IAAI;IAQZ;;OAEG;IACH,IAAI,IAAI,IAAI;IAQZ;;OAEG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAS9D;;OAEG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;CAQ/D;AAED;;GAEG;AACH,wBAAgB,qBAAqB,CACnC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAC5B,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,OAAO,CAAC,cAAc,CAAM,GACpC,OAAO,CAST;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAClC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAC5B,QAAQ,GAAE;IAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;IAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;CAAsC,GAC5F,OAAO,CAWT;AAED,eAAe,OAAO,CAAC"}