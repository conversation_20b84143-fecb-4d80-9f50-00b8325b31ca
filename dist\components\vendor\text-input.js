/**
 * Advanced Text Input Component
 *
 * Provides enhanced text input functionality with cursor management,
 * keyboard shortcuts, line continuation, text selection, and paste handling.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export class TextInput extends EventEmitter {
    element;
    options;
    cursorPosition = 0;
    selectionStart = -1;
    selectionEnd = -1;
    history = [];
    historyIndex = -1;
    clipboard = '';
    constructor(options) {
        super();
        this.options = options;
        this.element = blessed.textarea({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || '100%',
            height: options.height || 3,
            border: options.border || { type: 'line' },
            style: {
                fg: 'white',
                bg: 'black',
                border: { fg: 'cyan' },
                focus: { border: { fg: 'yellow' } },
                ...options.style,
            },
            label: options.label,
            hidden: options.hidden,
            inputOnFocus: true,
            scrollable: true,
            keys: true,
            mouse: true,
            tags: false,
        });
        this.setupEventHandlers();
        this.updatePlaceholder();
    }
    setupEventHandlers() {
        // Basic input handling
        this.element.on('keypress', (ch, key) => {
            this.handleKeyPress(ch, key);
            if (this.options.onKeyPress) {
                this.options.onKeyPress(ch, key);
            }
        });
        // Submit on Enter (unless multiline)
        this.element.key(['enter'], () => {
            if (!this.options.multiline) {
                this.submit();
            }
            else {
                this.insertText('\n');
            }
        });
        // Ctrl+Enter for submit in multiline mode
        this.element.key(['C-enter'], () => {
            if (this.options.multiline) {
                this.submit();
            }
        });
        // Navigation shortcuts
        this.element.key(['C-a'], () => this.moveToStart());
        this.element.key(['C-e'], () => this.moveToEnd());
        this.element.key(['C-k'], () => this.deleteToEnd());
        this.element.key(['C-u'], () => this.deleteToStart());
        this.element.key(['C-w'], () => this.deleteWord());
        this.element.key(['M-f'], () => this.moveWordForward());
        this.element.key(['M-b'], () => this.moveWordBackward());
        // Copy/Paste
        this.element.key(['C-c'], () => this.copy());
        this.element.key(['C-v'], () => this.paste());
        this.element.key(['C-x'], () => this.cut());
        // History navigation
        this.element.key(['up'], () => this.historyUp());
        this.element.key(['down'], () => this.historyDown());
        // Selection
        this.element.key(['S-left'], () => this.selectLeft());
        this.element.key(['S-right'], () => this.selectRight());
        this.element.key(['C-S-a'], () => this.selectAll());
        // Tab completion
        this.element.key(['tab'], () => {
            this.emit('tab', this.getValue());
        });
        // Value change events
        this.element.on('value', () => {
            if (this.options.onChange) {
                this.options.onChange(this.getValue());
            }
            this.emit('change', this.getValue());
        });
        // Focus events
        this.element.on('focus', () => {
            this.emit('focus');
        });
        this.element.on('blur', () => {
            this.emit('blur');
        });
    }
    handleKeyPress(ch, key) {
        // Handle special characters and key combinations
        if (key.name === 'backspace') {
            this.handleBackspace();
        }
        else if (key.name === 'delete') {
            this.handleDelete();
        }
        else if (key.name === 'left') {
            this.moveCursorLeft();
        }
        else if (key.name === 'right') {
            this.moveCursorRight();
        }
        else if (ch && ch.length === 1 && !key.ctrl && !key.meta) {
            this.insertText(ch);
        }
        this.updateDisplay();
    }
    insertText(text) {
        const currentValue = this.getValue();
        const maxLength = this.options.maxLength;
        if (maxLength && currentValue.length + text.length > maxLength) {
            return;
        }
        if (this.hasSelection()) {
            this.deleteSelection();
        }
        const before = currentValue.substring(0, this.cursorPosition);
        const after = currentValue.substring(this.cursorPosition);
        const newValue = before + text + after;
        this.setValue(newValue);
        this.cursorPosition += text.length;
        this.clearSelection();
    }
    handleBackspace() {
        if (this.hasSelection()) {
            this.deleteSelection();
        }
        else if (this.cursorPosition > 0) {
            const currentValue = this.getValue();
            const before = currentValue.substring(0, this.cursorPosition - 1);
            const after = currentValue.substring(this.cursorPosition);
            this.setValue(before + after);
            this.cursorPosition--;
        }
    }
    handleDelete() {
        if (this.hasSelection()) {
            this.deleteSelection();
        }
        else {
            const currentValue = this.getValue();
            if (this.cursorPosition < currentValue.length) {
                const before = currentValue.substring(0, this.cursorPosition);
                const after = currentValue.substring(this.cursorPosition + 1);
                this.setValue(before + after);
            }
        }
    }
    moveCursorLeft() {
        if (this.cursorPosition > 0) {
            this.cursorPosition--;
            this.clearSelection();
        }
    }
    moveCursorRight() {
        const currentValue = this.getValue();
        if (this.cursorPosition < currentValue.length) {
            this.cursorPosition++;
            this.clearSelection();
        }
    }
    moveToStart() {
        this.cursorPosition = 0;
        this.clearSelection();
    }
    moveToEnd() {
        this.cursorPosition = this.getValue().length;
        this.clearSelection();
    }
    deleteToEnd() {
        const currentValue = this.getValue();
        const newValue = currentValue.substring(0, this.cursorPosition);
        this.setValue(newValue);
    }
    deleteToStart() {
        const currentValue = this.getValue();
        const newValue = currentValue.substring(this.cursorPosition);
        this.setValue(newValue);
        this.cursorPosition = 0;
    }
    deleteWord() {
        const currentValue = this.getValue();
        const before = currentValue.substring(0, this.cursorPosition);
        const after = currentValue.substring(this.cursorPosition);
        // Find word boundary
        const wordMatch = before.match(/\S+\s*$/);
        if (wordMatch) {
            const newBefore = before.substring(0, before.length - wordMatch[0].length);
            this.setValue(newBefore + after);
            this.cursorPosition = newBefore.length;
        }
    }
    moveWordForward() {
        const currentValue = this.getValue();
        const after = currentValue.substring(this.cursorPosition);
        const wordMatch = after.match(/^\s*\S+/);
        if (wordMatch) {
            this.cursorPosition += wordMatch[0].length;
        }
        else {
            this.cursorPosition = currentValue.length;
        }
        this.clearSelection();
    }
    moveWordBackward() {
        const currentValue = this.getValue();
        const before = currentValue.substring(0, this.cursorPosition);
        const wordMatch = before.match(/\S+\s*$/);
        if (wordMatch) {
            this.cursorPosition -= wordMatch[0].length;
        }
        else {
            this.cursorPosition = 0;
        }
        this.clearSelection();
    }
    hasSelection() {
        return this.selectionStart !== -1 && this.selectionEnd !== -1;
    }
    clearSelection() {
        this.selectionStart = -1;
        this.selectionEnd = -1;
    }
    deleteSelection() {
        if (!this.hasSelection())
            return;
        const start = Math.min(this.selectionStart, this.selectionEnd);
        const end = Math.max(this.selectionStart, this.selectionEnd);
        const currentValue = this.getValue();
        const before = currentValue.substring(0, start);
        const after = currentValue.substring(end);
        this.setValue(before + after);
        this.cursorPosition = start;
        this.clearSelection();
    }
    selectLeft() {
        if (this.selectionStart === -1) {
            this.selectionStart = this.cursorPosition;
        }
        if (this.cursorPosition > 0) {
            this.cursorPosition--;
        }
        this.selectionEnd = this.cursorPosition;
    }
    selectRight() {
        if (this.selectionStart === -1) {
            this.selectionStart = this.cursorPosition;
        }
        const currentValue = this.getValue();
        if (this.cursorPosition < currentValue.length) {
            this.cursorPosition++;
        }
        this.selectionEnd = this.cursorPosition;
    }
    selectAll() {
        this.selectionStart = 0;
        this.selectionEnd = this.getValue().length;
        this.cursorPosition = this.selectionEnd;
    }
    copy() {
        if (this.hasSelection()) {
            const start = Math.min(this.selectionStart, this.selectionEnd);
            const end = Math.max(this.selectionStart, this.selectionEnd);
            this.clipboard = this.getValue().substring(start, end);
        }
    }
    paste() {
        if (this.clipboard) {
            this.insertText(this.clipboard);
        }
    }
    cut() {
        this.copy();
        this.deleteSelection();
    }
    historyUp() {
        if (this.history.length === 0)
            return;
        if (this.historyIndex === -1) {
            this.historyIndex = this.history.length - 1;
        }
        else if (this.historyIndex > 0) {
            this.historyIndex--;
        }
        this.setValue(this.history[this.historyIndex]);
        this.moveToEnd();
    }
    historyDown() {
        if (this.historyIndex === -1)
            return;
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.setValue(this.history[this.historyIndex]);
        }
        else {
            this.historyIndex = -1;
            this.setValue('');
        }
        this.moveToEnd();
    }
    submit() {
        const value = this.getValue();
        // Add to history if not empty and different from last entry
        if (value.trim() && (this.history.length === 0 || this.history[this.history.length - 1] !== value)) {
            this.history.push(value);
            // Limit history size
            if (this.history.length > 100) {
                this.history.shift();
            }
        }
        this.historyIndex = -1;
        if (this.options.onSubmit) {
            this.options.onSubmit(value);
        }
        this.emit('submit', value);
    }
    updateDisplay() {
        this.updatePlaceholder();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    updatePlaceholder() {
        if (this.options.placeholder && !this.getValue()) {
            // Show placeholder text
            this.element.style.fg = 'gray';
        }
        else {
            this.element.style.fg = this.options.style?.fg || 'white';
        }
    }
    // Public API
    getValue() {
        return this.element.getValue();
    }
    setValue(value) {
        this.element.setValue(value);
        this.cursorPosition = Math.min(this.cursorPosition, value.length);
        this.clearSelection();
        this.updateDisplay();
    }
    clear() {
        this.setValue('');
        this.cursorPosition = 0;
    }
    focus() {
        this.element.focus();
    }
    blur() {
        // Type assertion since blessed types don't include blur method
        this.element.blur?.();
    }
    show() {
        this.element.show();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    hide() {
        this.element.hide();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    destroy() {
        this.element.destroy();
    }
    getElement() {
        return this.element;
    }
    setPlaceholder(placeholder) {
        this.options.placeholder = placeholder;
        this.updatePlaceholder();
    }
    addToHistory(value) {
        if (value.trim() && (this.history.length === 0 || this.history[this.history.length - 1] !== value)) {
            this.history.push(value);
            if (this.history.length > 100) {
                this.history.shift();
            }
        }
    }
    getHistory() {
        return [...this.history];
    }
    clearHistory() {
        this.history = [];
        this.historyIndex = -1;
    }
}
export default TextInput;
//# sourceMappingURL=text-input.js.map