/**
 * Shell Command Execution Handler
 *
 * Handles command processing pipeline with platform adaptation,
 * security validation, and result formatting.
 */
import { spawn } from 'child_process';
import { resolve } from 'path';
import { logInfo, logError, logDebug, createTimer } from '../logger/log.js';
import { adaptCommandForPlatform } from './platform-commands.js';
/**
 * Handle command execution with full pipeline processing
 */
export async function handleExecCommand(input, config) {
    const timer = createTimer('exec-command');
    try {
        logInfo(`Executing command: ${input.command.join(' ')}`);
        // Validate and prepare command
        const processedInput = await preprocessCommand(input, config);
        // Check approval requirements
        if (requiresApproval(processedInput, config)) {
            const approved = await requestApproval(processedInput, config);
            if (!approved) {
                return createErrorResult(processedInput, 'Command execution denied by user');
            }
        }
        // Execute command
        const result = await executeCommand(processedInput, config);
        timer.end();
        return result;
    }
    catch (error) {
        timer.end();
        logError('Command execution failed', error);
        return createErrorResult(input, error.message);
    }
}
/**
 * Preprocess command for execution
 */
async function preprocessCommand(input, config) {
    // Adapt command for current platform
    const adaptedCommand = adaptCommandForPlatform(input.command);
    // Resolve working directory
    const workdir = input.workdir ? resolve(input.workdir) : (config.workingDirectory || process.cwd());
    // Apply security constraints
    const secureCommand = applySecurityConstraints(adaptedCommand, config);
    return {
        ...input,
        command: secureCommand,
        workdir,
        timeout: input.timeout || 30000,
    };
}
/**
 * Apply security constraints to command
 */
function applySecurityConstraints(command, config) {
    // Remove potentially dangerous flags
    const filteredCommand = command.filter(arg => {
        // Filter out dangerous flags
        if (arg.startsWith('--force') || arg.startsWith('-f')) {
            logDebug(`Filtered dangerous flag: ${arg}`);
            return false;
        }
        // Filter out recursive delete patterns
        if (arg.includes('rm') && (arg.includes('-rf') || arg.includes('-r'))) {
            logDebug(`Filtered dangerous recursive delete: ${arg}`);
            return false;
        }
        return true;
    });
    return filteredCommand;
}
/**
 * Check if command requires approval
 */
function requiresApproval(input, config) {
    if (config.approvalPolicy === 'full-auto') {
        return false;
    }
    if (config.approvalPolicy === 'suggest') {
        return true;
    }
    // For auto-edit mode, check if command is safe
    const commandName = input.command[0]?.toLowerCase();
    const safeCommands = config.safeCommands || [
        'ls', 'dir', 'cat', 'type', 'grep', 'findstr', 'head', 'tail',
        'pwd', 'cd', 'echo', 'which', 'where', 'git', 'npm', 'node',
    ];
    return !safeCommands.includes(commandName);
}
/**
 * Request approval for command execution
 */
async function requestApproval(input, config) {
    // For now, return true (approval UI will be implemented later)
    logInfo(`Command requires approval: ${input.command.join(' ')}`);
    return true;
}
/**
 * Execute command with proper error handling and output capture
 */
async function executeCommand(input, config) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        const [command, ...args] = input.command;
        logDebug(`Spawning process: ${command} ${args.join(' ')}`);
        const child = spawn(command, args, {
            cwd: input.workdir,
            env: { ...process.env, ...input.env },
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: process.platform === 'win32',
        });
        let stdout = '';
        let stderr = '';
        let killed = false;
        // Set up timeout
        const timeout = setTimeout(() => {
            if (!killed) {
                killed = true;
                child.kill('SIGTERM');
                // Force kill after 5 seconds
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }
        }, input.timeout);
        // Capture output
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        // Handle process completion
        child.on('close', (code, signal) => {
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            const success = code === 0 && !killed;
            const result = {
                success,
                exitCode: code || (killed ? -1 : 0),
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                command: input.command,
                workdir: input.workdir || process.cwd(),
                duration,
                metadata: {
                    killed,
                    signal,
                    timeout: input.timeout,
                },
            };
            logDebug(`Command completed: exit code ${code}, duration ${duration}ms`);
            resolve(result);
        });
        // Handle process errors
        child.on('error', (error) => {
            clearTimeout(timeout);
            const result = {
                success: false,
                exitCode: -1,
                stdout: stdout.trim(),
                stderr: error.message,
                command: input.command,
                workdir: input.workdir || process.cwd(),
                duration: Date.now() - startTime,
                metadata: {
                    error: error.message,
                },
            };
            resolve(result);
        });
    });
}
/**
 * Create error result
 */
function createErrorResult(input, errorMessage) {
    return {
        success: false,
        exitCode: -1,
        stdout: '',
        stderr: errorMessage,
        command: input.command,
        workdir: input.workdir || process.cwd(),
        duration: 0,
        metadata: {
            error: errorMessage,
        },
    };
}
/**
 * Format command result for display
 */
export function formatExecResult(result) {
    const lines = [];
    lines.push(`Command: ${result.command.join(' ')}`);
    lines.push(`Working Directory: ${result.workdir}`);
    lines.push(`Exit Code: ${result.exitCode}`);
    lines.push(`Duration: ${result.duration}ms`);
    lines.push(`Success: ${result.success ? 'Yes' : 'No'}`);
    if (result.stdout) {
        lines.push('');
        lines.push('STDOUT:');
        lines.push(result.stdout);
    }
    if (result.stderr) {
        lines.push('');
        lines.push('STDERR:');
        lines.push(result.stderr);
    }
    return lines.join('\n');
}
/**
 * Validate command safety
 */
export function validateCommandSafety(command) {
    const warnings = [];
    const blocked = [];
    const commandName = command[0]?.toLowerCase();
    const fullCommand = command.join(' ').toLowerCase();
    // Check for dangerous commands
    const dangerousCommands = [
        'rm -rf /',
        'del /s /q',
        'format',
        'fdisk',
        'dd if=',
        'sudo rm',
        'chmod 777',
    ];
    for (const dangerous of dangerousCommands) {
        if (fullCommand.includes(dangerous)) {
            blocked.push(`Dangerous command pattern: ${dangerous}`);
        }
    }
    // Check for potentially risky operations
    const riskyPatterns = [
        'rm -rf',
        'del /s',
        'sudo',
        'chmod',
        'chown',
        'passwd',
    ];
    for (const risky of riskyPatterns) {
        if (fullCommand.includes(risky)) {
            warnings.push(`Potentially risky operation: ${risky}`);
        }
    }
    return {
        safe: blocked.length === 0,
        warnings,
        blocked,
    };
}
/**
 * Get command execution statistics
 */
export function getExecStats() {
    // This would be implemented with persistent storage
    return {
        totalCommands: 0,
        successfulCommands: 0,
        failedCommands: 0,
        averageDuration: 0,
        mostUsedCommands: [],
    };
}
//# sourceMappingURL=handle-exec-command.js.map