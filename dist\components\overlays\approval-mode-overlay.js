/**
 * Approval Mode Overlay Component
 *
 * Allows users to configure command approval policies and security settings.
 */
import blessed from 'blessed';
export class ApprovalModeOverlay {
    screen;
    container;
    modeList;
    detailsBox;
    statusBar;
    modes = [];
    selectedMode;
    options;
    constructor(options) {
        this.options = options;
        this.screen = options.parent;
        this.selectedMode = options.currentMode;
        this.initializeModes();
        this.createComponents();
        this.setupEventHandlers();
        this.updateDisplay();
    }
    /**
     * Initialize approval mode definitions
     */
    initializeModes() {
        this.modes = [
            {
                mode: 'suggest',
                title: 'Suggest Mode',
                description: 'Manual approval required for all commands and file operations.',
                security: 'High',
                features: [
                    '✓ Manual approval for every command',
                    '✓ Manual approval for file edits',
                    '✓ Full control over AI actions',
                    '✓ Maximum security',
                    '✗ Requires constant interaction',
                ],
            },
            {
                mode: 'auto-edit',
                title: 'Auto-Edit Mode',
                description: 'Automatic file edits, manual approval for system commands.',
                security: 'Medium',
                features: [
                    '✓ Automatic file creation/editing',
                    '✓ Manual approval for shell commands',
                    '✓ Balanced automation and control',
                    '✓ Good for development workflows',
                    '⚠ File changes happen automatically',
                ],
            },
            {
                mode: 'full-auto',
                title: 'Full-Auto Mode',
                description: 'Automatic execution of all commands with sandboxing.',
                security: 'Low',
                features: [
                    '✓ Fully autonomous operation',
                    '✓ Sandboxed command execution',
                    '✓ Maximum productivity',
                    '⚠ Commands run automatically',
                    '⚠ Use only in trusted environments',
                ],
            },
        ];
    }
    /**
     * Create UI components
     */
    createComponents() {
        // Main container
        this.container = blessed.box({
            parent: this.screen,
            top: 'center',
            left: 'center',
            width: '85%',
            height: '75%',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'cyan',
                },
            },
            label: ' Approval Mode Configuration ',
            tags: true,
            keys: true,
            vi: true,
        });
        // Mode list
        this.modeList = blessed.list({
            parent: this.container,
            top: 0,
            left: 0,
            width: '35%',
            height: '100%-3',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'yellow',
                },
                selected: {
                    bg: 'blue',
                },
            },
            label: ' Modes ',
            keys: true,
            vi: true,
            scrollable: true,
            mouse: true,
        });
        // Details box
        this.detailsBox = blessed.box({
            parent: this.container,
            top: 0,
            left: '35%',
            width: '65%',
            height: '100%-3',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'green',
                },
            },
            label: ' Details ',
            scrollable: true,
            alwaysScroll: true,
            tags: true,
            keys: true,
            vi: true,
        });
        // Status bar
        this.statusBar = blessed.box({
            parent: this.container,
            bottom: 0,
            left: 0,
            width: '100%',
            height: 3,
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'gray',
                },
            },
            content: 'Enter: Select | Esc: Close | Up/Down: Navigate',
            tags: true,
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Global key handlers
        this.container.key(['escape', 'q'], () => {
            this.close();
        });
        this.container.key(['enter'], () => {
            this.selectCurrent();
        });
        // Mode list handlers
        this.modeList.on('select', () => {
            this.selectCurrent();
        });
        this.modeList.key(['j', 'down'], () => {
            this.modeList.down(1);
            this.updateDetails();
            this.screen.render();
        });
        this.modeList.key(['k', 'up'], () => {
            this.modeList.up(1);
            this.updateDetails();
            this.screen.render();
        });
        // Details box scroll
        this.detailsBox.key(['j', 'down'], () => {
            this.detailsBox.scroll(1);
            this.screen.render();
        });
        this.detailsBox.key(['k', 'up'], () => {
            this.detailsBox.scroll(-1);
            this.screen.render();
        });
        // Focus management
        this.modeList.focus();
    }
    /**
     * Update display
     */
    updateDisplay() {
        this.updateModeList();
        this.updateDetails();
    }
    /**
     * Update mode list
     */
    updateModeList() {
        const items = this.modes.map(mode => {
            const current = mode.mode === this.selectedMode ? '●' : '○';
            const securityColor = mode.security === 'High' ? 'green-fg' :
                mode.security === 'Medium' ? 'yellow-fg' : 'red-fg';
            return `${current} ${mode.title} {${securityColor}}[${mode.security}]{/${securityColor}}`;
        });
        this.modeList.setItems(items);
        // Select current mode
        const currentIndex = this.modes.findIndex(mode => mode.mode === this.selectedMode);
        if (currentIndex >= 0) {
            this.modeList.select(currentIndex);
        }
        this.screen.render();
    }
    /**
     * Update details display
     */
    updateDetails() {
        const selected = this.modeList.selected;
        if (selected >= 0 && selected < this.modes.length) {
            const mode = this.modes[selected];
            const securityColor = mode.security === 'High' ? 'green-fg' :
                mode.security === 'Medium' ? 'yellow-fg' : 'red-fg';
            const content = [
                `{bold}{cyan-fg}${mode.title}{/cyan-fg}{/bold}`,
                '',
                `{bold}Security Level:{/bold} {${securityColor}}${mode.security}{/${securityColor}}`,
                '',
                `{bold}Description:{/bold}`,
                mode.description,
                '',
                `{bold}Features:{/bold}`,
                ...mode.features.map(feature => `  ${feature}`),
                '',
                `{bold}Use Cases:{/bold}`,
                ...this.getUseCases(mode.mode),
                '',
                `{bold}Considerations:{/bold}`,
                ...this.getConsiderations(mode.mode),
            ];
            this.detailsBox.setContent(content.join('\n'));
        }
        this.screen.render();
    }
    /**
     * Get use cases for mode
     */
    getUseCases(mode) {
        switch (mode) {
            case 'suggest':
                return [
                    '• Production environments',
                    '• Sensitive codebases',
                    '• Learning and exploration',
                    '• When maximum control is needed',
                ];
            case 'auto-edit':
                return [
                    '• Development environments',
                    '• Code refactoring tasks',
                    '• Documentation generation',
                    '• Routine development work',
                ];
            case 'full-auto':
                return [
                    '• Trusted development environments',
                    '• Automated workflows',
                    '• Rapid prototyping',
                    '• Sandboxed environments',
                ];
            default:
                return [];
        }
    }
    /**
     * Get considerations for mode
     */
    getConsiderations(mode) {
        switch (mode) {
            case 'suggest':
                return [
                    '• Requires manual approval for every action',
                    '• Slower workflow but maximum safety',
                    '• Best for beginners or critical systems',
                ];
            case 'auto-edit':
                return [
                    '• Files will be modified automatically',
                    '• Commands still require approval',
                    '• Good balance of speed and safety',
                ];
            case 'full-auto':
                return [
                    '• Commands execute without confirmation',
                    '• Only use in trusted environments',
                    '• Sandboxing provides some protection',
                    '• Monitor AI actions carefully',
                ];
            default:
                return [];
        }
    }
    /**
     * Select current mode
     */
    selectCurrent() {
        const selected = this.modeList.selected;
        if (selected >= 0 && selected < this.modes.length) {
            const mode = this.modes[selected];
            this.selectedMode = mode.mode;
            if (this.options.onSelect) {
                this.options.onSelect(mode.mode);
            }
            this.close();
        }
    }
    /**
     * Show the overlay
     */
    show() {
        this.container.show();
        this.modeList.focus();
        this.screen.render();
    }
    /**
     * Hide the overlay
     */
    hide() {
        this.container.hide();
        this.screen.render();
    }
    /**
     * Close the overlay
     */
    close() {
        this.hide();
        if (this.options.onClose) {
            this.options.onClose();
        }
    }
    /**
     * Destroy the overlay
     */
    destroy() {
        this.container.destroy();
    }
}
export default ApprovalModeOverlay;
//# sourceMappingURL=approval-mode-overlay.js.map