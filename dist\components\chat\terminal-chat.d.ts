/**
 * Terminal Chat Interface
 *
 * Main terminal-based chat interface using blessed.js for rich UI.
 * Handles real-time conversation, model switching, and overlays.
 */
import { EventEmitter } from 'events';
import type { AppConfig } from '../../types/index.js';
export type OverlayModeType = 'none' | 'history' | 'sessions' | 'model' | 'approval' | 'help' | 'diff';
export interface TerminalChatOptions {
    config: AppConfig;
    initialPrompt?: string;
}
export declare class TerminalChat extends EventEmitter {
    private config;
    private screen;
    private chatContainer;
    private inputContainer;
    private statusBar;
    private agentLoop;
    private sessionId;
    private items;
    private currentModel;
    private currentProvider;
    private approvalPolicy;
    private overlayMode;
    private loading;
    private terminalSize;
    private chatInput;
    private thinkingAnimation;
    private currentOverlay;
    private notificationTimeout;
    constructor(options: TerminalChatOptions);
    /**
     * Initialize the blessed screen
     */
    private initializeScreen;
    /**
     * Initialize UI components
     */
    private initializeComponents;
    /**
     * Initialize chat input component
     */
    private initializeChatInput;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Get status bar content
     */
    private getStatusBarContent;
    /**
     * Update layout after resize
     */
    private updateLayout;
    /**
     * Update input height
     */
    private updateInputHeight;
    /**
     * Handle user input
     */
    private handleUserInput;
    /**
     * Handle cancel operation
     */
    private handleCancel;
    /**
     * Handle slash commands
     */
    private handleSlashCommand;
    /**
     * Process initial prompt
     */
    private processInitialPrompt;
    /**
     * Show thinking animation
     */
    private showThinkingAnimation;
    /**
     * Hide thinking animation
     */
    private hideThinkingAnimation;
    /**
     * Add message to chat
     */
    private addMessageToChat;
    /**
     * Add response to chat
     */
    private addResponseToChat;
    /**
     * Add error to chat
     */
    private addErrorToChat;
    /**
     * Add system message to chat
     */
    private addSystemMessage;
    /**
     * Update chat display
     */
    private updateChatDisplay;
    /**
     * Render chat item
     */
    private renderChatItem;
    /**
     * Render content array
     */
    private renderContent;
    /**
     * Show overlay
     */
    private showOverlay;
    /**
     * Close current overlay
     */
    private closeOverlay;
    /**
     * Handle history selection
     */
    private handleHistorySelect;
    /**
     * Handle model selection
     */
    private handleModelSelect;
    /**
     * Handle session selection
     */
    private handleSessionSelect;
    /**
     * Handle approval mode selection
     */
    private handleApprovalModeSelect;
    /**
     * Show diff overlay
     */
    private showDiffOverlay;
    /**
     * Clear chat
     */
    private clearChat;
    /**
     * Compact conversation
     */
    private compactConversation;
    /**
     * Generate bug report
     */
    private generateBugReport;
    /**
     * Save session
     */
    private saveSession;
    /**
     * Show response notification
     */
    private showResponseNotification;
    /**
     * Get response preview for notifications
     */
    private getResponsePreview;
    /**
     * Start the chat interface
     */
    start(): void;
    /**
     * Exit the application
     */
    exit(): void;
    /**
     * Destroy the chat interface
     */
    destroy(): void;
}
//# sourceMappingURL=terminal-chat.d.ts.map