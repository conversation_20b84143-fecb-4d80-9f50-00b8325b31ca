/**
 * Enhanced Box Component
 *
 * Wrapper around blessed.box with additional functionality
 * for layout management and styling.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export interface EnhancedBoxOptions extends blessed.Widgets.BoxOptions {
    onResize?: (width: number, height: number) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    onClick?: (data: any) => void;
    theme?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    padding?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    margin?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
}
export declare class EnhancedBox extends EventEmitter {
    private box;
    private options;
    private children;
    constructor(options?: EnhancedBoxOptions);
    /**
     * Apply theme styling
     */
    private applyTheme;
    /**
     * Apply padding and margin
     */
    private applySpacing;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Get the underlying blessed box
     */
    getBox(): blessed.Widgets.BoxElement;
    /**
     * Set content
     */
    setContent(content: string): void;
    /**
     * Get content
     */
    getContent(): string;
    /**
     * Append content
     */
    appendContent(content: string): void;
    /**
     * Clear content
     */
    clear(): void;
    /**
     * Add child element
     */
    append(child: blessed.Widgets.Node): void;
    /**
     * Remove child element
     */
    remove(child: blessed.Widgets.Node): void;
    /**
     * Insert child at index
     */
    insert(child: blessed.Widgets.Node, index: number): void;
    /**
     * Get all children
     */
    getChildren(): blessed.Widgets.Node[];
    /**
     * Focus the box
     */
    focus(): void;
    /**
     * Show the box
     */
    show(): void;
    /**
     * Hide the box
     */
    hide(): void;
    /**
     * Toggle visibility
     */
    toggle(): void;
    /**
     * Set position
     */
    setPosition(options: {
        top?: number | string;
        left?: number | string;
        width?: number | string;
        height?: number | string;
    }): void;
    /**
     * Get position
     */
    getPosition(): {
        top: number;
        left: number;
        width: number;
        height: number;
    };
    /**
     * Set style
     */
    setStyle(style: any): void;
    /**
     * Set border
     */
    setBorder(border: blessed.Widgets.Border): void;
    /**
     * Enable scrolling
     */
    enableScrolling(): void;
    /**
     * Disable scrolling
     */
    disableScrolling(): void;
    /**
     * Scroll to position
     */
    scrollTo(position: number): void;
    /**
     * Scroll by amount
     */
    scroll(amount: number): void;
    /**
     * Get scroll position
     */
    getScrollPosition(): number;
    /**
     * Render the box
     */
    render(): void;
    /**
     * Check if box is visible
     */
    get visible(): boolean;
    /**
     * Get box dimensions
     */
    get dimensions(): {
        width: number;
        height: number;
    };
    /**
     * Destroy the box
     */
    destroy(): void;
}
export default EnhancedBox;
//# sourceMappingURL=box.d.ts.map