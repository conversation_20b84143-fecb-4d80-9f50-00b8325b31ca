/**
 * Desktop Notifications System
 *
 * Cross-platform desktop notifications for important events,
 * command completions, and user alerts with smart throttling.
 */
import notifier from 'node-notifier';
import { join } from 'path';
import { logDebug, logError } from './logger/log.js';
/**
 * Notification manager with throttling and configuration
 */
export class NotificationManager {
    config;
    recentNotifications = new Map();
    notificationCount = 0;
    lastMinuteReset = Date.now();
    constructor(config = {}) {
        this.config = {
            enabled: true,
            soundEnabled: true,
            timeout: 5000,
            throttleMs: 2000,
            maxPerMinute: 10,
            ...config,
        };
    }
    /**
     * Send a notification with throttling
     */
    async notify(options) {
        if (!this.config.enabled) {
            return;
        }
        // Check rate limiting
        if (!this.checkRateLimit(options)) {
            logDebug('Notification throttled', { title: options.title });
            return;
        }
        try {
            const notificationOptions = this.buildNotificationOptions(options);
            // Send notification
            notifier.notify(notificationOptions, (error, response, metadata) => {
                if (error) {
                    logError('Notification error', error);
                    return;
                }
                // Handle click events
                if (response === 'activate' && options.onClick) {
                    options.onClick();
                }
                // Handle timeout events
                if (response === 'timeout' && options.onTimeout) {
                    options.onTimeout();
                }
                logDebug('Notification sent', {
                    title: options.title,
                    response,
                    metadata
                });
            });
            // Track notification for throttling
            this.trackNotification(options);
        }
        catch (error) {
            logError('Failed to send notification', error);
        }
    }
    /**
     * Send success notification
     */
    async success(title, message, options = {}) {
        await this.notify({
            title,
            message,
            type: 'success',
            sound: true,
            ...options,
        });
    }
    /**
     * Send error notification
     */
    async error(title, message, options = {}) {
        await this.notify({
            title,
            message,
            type: 'error',
            sound: true,
            ...options,
        });
    }
    /**
     * Send warning notification
     */
    async warning(title, message, options = {}) {
        await this.notify({
            title,
            message,
            type: 'warning',
            sound: false,
            ...options,
        });
    }
    /**
     * Send info notification
     */
    async info(title, message, options = {}) {
        await this.notify({
            title,
            message,
            type: 'info',
            sound: false,
            ...options,
        });
    }
    /**
     * Send command completion notification
     */
    async commandComplete(command, success, duration) {
        const title = success ? 'Command Completed' : 'Command Failed';
        const message = `${command} (${this.formatDuration(duration)})`;
        const type = success ? 'success' : 'error';
        await this.notify({
            title,
            message,
            type,
            sound: !success, // Only sound on failures
        });
    }
    /**
     * Send agent action notification
     */
    async agentAction(action, details) {
        await this.notify({
            title: 'AI Agent Action',
            message: details ? `${action}: ${details}` : action,
            type: 'info',
            sound: false,
        });
    }
    /**
     * Build notification options for node-notifier
     */
    buildNotificationOptions(options) {
        const baseOptions = {
            title: options.title,
            message: options.message,
            timeout: options.timeout || this.config.timeout,
            sound: this.config.soundEnabled && (options.sound ?? false),
            wait: true, // Wait for user interaction
        };
        // Add type-specific styling
        switch (options.type) {
            case 'success':
                return {
                    ...baseOptions,
                    icon: options.icon || this.getSuccessIcon(),
                    sound: this.config.soundEnabled && (options.sound ?? true),
                };
            case 'error':
                return {
                    ...baseOptions,
                    icon: options.icon || this.getErrorIcon(),
                    sound: this.config.soundEnabled && (options.sound ?? true),
                };
            case 'warning':
                return {
                    ...baseOptions,
                    icon: options.icon || this.getWarningIcon(),
                };
            case 'info':
            default:
                return {
                    ...baseOptions,
                    icon: options.icon || this.getInfoIcon(),
                };
        }
    }
    /**
     * Check rate limiting
     */
    checkRateLimit(options) {
        const now = Date.now();
        const key = `${options.title}:${options.type}`;
        // Reset per-minute counter
        if (now - this.lastMinuteReset > 60000) {
            this.notificationCount = 0;
            this.lastMinuteReset = now;
        }
        // Check per-minute limit
        if (this.notificationCount >= this.config.maxPerMinute) {
            return false;
        }
        // Check throttling for similar notifications
        const lastSent = this.recentNotifications.get(key);
        if (lastSent && now - lastSent < this.config.throttleMs) {
            return false;
        }
        return true;
    }
    /**
     * Track notification for throttling
     */
    trackNotification(options) {
        const now = Date.now();
        const key = `${options.title}:${options.type}`;
        this.recentNotifications.set(key, now);
        this.notificationCount++;
        // Clean up old entries
        for (const [k, timestamp] of this.recentNotifications.entries()) {
            if (now - timestamp > this.config.throttleMs * 2) {
                this.recentNotifications.delete(k);
            }
        }
    }
    /**
     * Get platform-appropriate icons
     */
    getSuccessIcon() {
        if (process.platform === 'darwin') {
            return join(__dirname, '../assets/icons/success.icns');
        }
        else if (process.platform === 'win32') {
            return join(__dirname, '../assets/icons/success.ico');
        }
        else {
            return join(__dirname, '../assets/icons/success.png');
        }
    }
    getErrorIcon() {
        if (process.platform === 'darwin') {
            return join(__dirname, '../assets/icons/error.icns');
        }
        else if (process.platform === 'win32') {
            return join(__dirname, '../assets/icons/error.ico');
        }
        else {
            return join(__dirname, '../assets/icons/error.png');
        }
    }
    getWarningIcon() {
        if (process.platform === 'darwin') {
            return join(__dirname, '../assets/icons/warning.icns');
        }
        else if (process.platform === 'win32') {
            return join(__dirname, '../assets/icons/warning.ico');
        }
        else {
            return join(__dirname, '../assets/icons/warning.png');
        }
    }
    getInfoIcon() {
        if (process.platform === 'darwin') {
            return join(__dirname, '../assets/icons/info.icns');
        }
        else if (process.platform === 'win32') {
            return join(__dirname, '../assets/icons/info.ico');
        }
        else {
            return join(__dirname, '../assets/icons/info.png');
        }
    }
    /**
     * Format duration for display
     */
    formatDuration(ms) {
        if (ms < 1000) {
            return `${ms}ms`;
        }
        else if (ms < 60000) {
            return `${(ms / 1000).toFixed(1)}s`;
        }
        else {
            const minutes = Math.floor(ms / 60000);
            const seconds = Math.floor((ms % 60000) / 1000);
            return `${minutes}m ${seconds}s`;
        }
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Enable/disable notifications
     */
    setEnabled(enabled) {
        this.config.enabled = enabled;
    }
    /**
     * Enable/disable sounds
     */
    setSoundEnabled(enabled) {
        this.config.soundEnabled = enabled;
    }
    /**
     * Clear notification history (for testing)
     */
    clearHistory() {
        this.recentNotifications.clear();
        this.notificationCount = 0;
        this.lastMinuteReset = Date.now();
    }
}
// Global notification manager instance
export const notifications = new NotificationManager();
// Convenience functions
export const notifySuccess = (title, message, options) => notifications.success(title, message, options);
export const notifyError = (title, message, options) => notifications.error(title, message, options);
export const notifyWarning = (title, message, options) => notifications.warning(title, message, options);
export const notifyInfo = (title, message, options) => notifications.info(title, message, options);
export const notifyCommandComplete = (command, success, duration) => notifications.commandComplete(command, success, duration);
export const notifyAgentAction = (action, details) => notifications.agentAction(action, details);
/**
 * Simple notification function for backward compatibility
 */
export const showNotification = (title, message, workingDirectory) => notifications.info(title, message, { workingDirectory });
export default notifications;
//# sourceMappingURL=notifications.js.map