/**
 * Command History Management
 * 
 * Persistent command history with sensitive data filtering,
 * search capabilities, and configurable size limits.
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { logInfo, logError, logWarn, toError } from '../logger/log.js';
import type { HistoryEntry, HistoryConfig } from '../../types/index.js';

// History configuration
const HISTORY_FILE = join(homedir(), '.kritrima-ai', 'command-history.json');
const DEFAULT_CONFIG: HistoryConfig = {
  maxSize: 1000,
  saveHistory: true,
  sensitivePatterns: [
    /api[_-]?key/i,
    /password/i,
    /secret/i,
    /token/i,
    /auth/i,
    /credential/i,
    /private[_-]?key/i,
    /access[_-]?key/i,
  ],
};

// In-memory cache
let historyCache: HistoryEntry[] | null = null;
let configCache: HistoryConfig | null = null;

/**
 * Add command to history
 */
export function addToHistory(
  command: string,
  sessionId?: string,
  success?: boolean
): void {
  try {
    const config = getHistoryConfig();
    
    if (!config.saveHistory) {
      return;
    }

    // Check for sensitive data
    if (containsSensitiveData(command, config.sensitivePatterns)) {
      logWarn('Command contains sensitive data, not saving to history');
      return;
    }

    const history = getHistory();
    
    // Don't add duplicate consecutive commands
    if (history.length > 0 && history[history.length - 1].command === command) {
      return;
    }

    const entry: HistoryEntry = {
      command,
      timestamp: Date.now(),
      sessionId,
      success,
    };

    history.push(entry);

    // Trim history if it exceeds max size
    if (history.length > config.maxSize) {
      history.splice(0, history.length - config.maxSize);
    }

    // Save to file
    saveHistory(history);
    
    logInfo(`Added command to history: ${command.substring(0, 50)}...`);
  } catch (error) {
    const err = toError(error);
    logError('Failed to add command to history', err);
  }
}

/**
 * Get command history
 */
export function getHistory(): HistoryEntry[] {
  if (historyCache) {
    return historyCache;
  }

  try {
    if (!existsSync(HISTORY_FILE)) {
      historyCache = [];
      return historyCache;
    }

    const content = readFileSync(HISTORY_FILE, 'utf-8');
    const data = JSON.parse(content);
    
    // Validate and migrate old format if needed
    historyCache = Array.isArray(data) ? data : data.entries || [];
    
    return historyCache;
  } catch (error) {
    logError('Failed to load command history', error);
    historyCache = [];
    return historyCache;
  }
}

/**
 * Save history to file
 */
function saveHistory(history: HistoryEntry[]): void {
  try {
    const dir = dirname(HISTORY_FILE);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    const data = {
      version: 1,
      entries: history,
      config: getHistoryConfig(),
      lastUpdated: Date.now(),
    };

    writeFileSync(HISTORY_FILE, JSON.stringify(data, null, 2));
    historyCache = history;
  } catch (error) {
    logError('Failed to save command history', error);
  }
}

/**
 * Get history configuration
 */
function getHistoryConfig(): HistoryConfig {
  if (configCache) {
    return configCache;
  }

  // Load from environment or use defaults
  configCache = {
    maxSize: parseInt(process.env.KRITRIMA_AI_HISTORY_SIZE || '1000', 10),
    saveHistory: process.env.KRITRIMA_AI_SAVE_HISTORY !== 'false',
    sensitivePatterns: DEFAULT_CONFIG.sensitivePatterns,
  };

  return configCache;
}

/**
 * Check if command contains sensitive data
 */
function containsSensitiveData(command: string, patterns: (string | RegExp)[]): boolean {
  for (const pattern of patterns) {
    if (pattern instanceof RegExp) {
      if (pattern.test(command)) {
        return true;
      }
    } else {
      if (command.toLowerCase().includes(pattern.toLowerCase())) {
        return true;
      }
    }
  }
  return false;
}

/**
 * Search command history
 */
export function searchHistory(query: string, limit = 50): HistoryEntry[] {
  const history = getHistory();
  const lowerQuery = query.toLowerCase();
  
  return history
    .filter(entry => entry.command.toLowerCase().includes(lowerQuery))
    .slice(-limit)
    .reverse(); // Most recent first
}

/**
 * Get recent commands
 */
export function getRecentCommands(limit = 20): HistoryEntry[] {
  const history = getHistory();
  return history.slice(-limit).reverse(); // Most recent first
}

/**
 * Get commands by session
 */
export function getCommandsBySession(sessionId: string): HistoryEntry[] {
  const history = getHistory();
  return history
    .filter(entry => entry.sessionId === sessionId)
    .reverse(); // Most recent first
}

/**
 * Get command statistics
 */
export function getHistoryStats(): {
  totalCommands: number;
  uniqueCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageCommandLength: number;
  mostUsedCommands: Array<{ command: string; count: number }>;
  oldestCommand?: Date;
  newestCommand?: Date;
} {
  const history = getHistory();
  
  if (history.length === 0) {
    return {
      totalCommands: 0,
      uniqueCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      averageCommandLength: 0,
      mostUsedCommands: [],
    };
  }

  const uniqueCommands = new Set(history.map(entry => entry.command));
  const successfulCommands = history.filter(entry => entry.success === true).length;
  const failedCommands = history.filter(entry => entry.success === false).length;
  
  const totalLength = history.reduce((sum, entry) => sum + entry.command.length, 0);
  const averageCommandLength = totalLength / history.length;

  // Count command frequency
  const commandCounts = new Map<string, number>();
  for (const entry of history) {
    const count = commandCounts.get(entry.command) || 0;
    commandCounts.set(entry.command, count + 1);
  }

  const mostUsedCommands = Array.from(commandCounts.entries())
    .map(([command, count]) => ({ command, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  const timestamps = history.map(entry => entry.timestamp).sort();
  const oldestCommand = new Date(timestamps[0]);
  const newestCommand = new Date(timestamps[timestamps.length - 1]);

  return {
    totalCommands: history.length,
    uniqueCommands: uniqueCommands.size,
    successfulCommands,
    failedCommands,
    averageCommandLength,
    mostUsedCommands,
    oldestCommand,
    newestCommand,
  };
}

/**
 * Clear command history
 */
export function clearHistory(): void {
  try {
    historyCache = [];
    saveHistory([]);
    logInfo('Command history cleared');
  } catch (error) {
    logError('Failed to clear command history', error);
  }
}

/**
 * Remove command from history
 */
export function removeFromHistory(index: number): boolean {
  try {
    const history = getHistory();
    
    if (index < 0 || index >= history.length) {
      return false;
    }

    history.splice(index, 1);
    saveHistory(history);
    
    logInfo(`Removed command at index ${index} from history`);
    return true;
  } catch (error) {
    logError('Failed to remove command from history', error);
    return false;
  }
}

/**
 * Update history configuration
 */
export function updateHistoryConfig(newConfig: Partial<HistoryConfig>): void {
  try {
    const currentConfig = getHistoryConfig();
    configCache = { ...currentConfig, ...newConfig };
    
    // Re-save history with new config
    const history = getHistory();
    saveHistory(history);
    
    logInfo('History configuration updated');
  } catch (error) {
    logError('Failed to update history configuration', error);
  }
}

/**
 * Export history to file
 */
export function exportHistory(outputPath: string): boolean {
  try {
    const history = getHistory();
    const data = {
      version: 1,
      exportDate: new Date().toISOString(),
      entries: history,
      stats: getHistoryStats(),
    };

    writeFileSync(outputPath, JSON.stringify(data, null, 2));
    logInfo(`History exported to: ${outputPath}`);
    return true;
  } catch (error) {
    logError(`Failed to export history to ${outputPath}`, error);
    return false;
  }
}

/**
 * Import history from file
 */
export function importHistory(inputPath: string, merge = false): boolean {
  try {
    const content = readFileSync(inputPath, 'utf-8');
    const data = JSON.parse(content);
    
    const importedEntries = data.entries || data;
    
    if (!Array.isArray(importedEntries)) {
      throw new Error('Invalid history format');
    }

    let history: HistoryEntry[];
    
    if (merge) {
      history = [...getHistory(), ...importedEntries];
    } else {
      history = importedEntries;
    }

    // Sort by timestamp and remove duplicates
    history.sort((a, b) => a.timestamp - b.timestamp);
    const uniqueHistory = history.filter((entry, index, arr) => 
      index === 0 || entry.command !== arr[index - 1].command || 
      entry.timestamp !== arr[index - 1].timestamp
    );

    saveHistory(uniqueHistory);
    logInfo(`History imported from: ${inputPath}`);
    return true;
  } catch (error) {
    logError(`Failed to import history from ${inputPath}`, error);
    return false;
  }
}

/**
 * Get command suggestions based on partial input
 */
export function getCommandSuggestions(partial: string, limit = 10): string[] {
  const history = getHistory();
  const lowerPartial = partial.toLowerCase();
  
  const suggestions = new Set<string>();
  
  for (const entry of history.reverse()) {
    if (entry.command.toLowerCase().startsWith(lowerPartial)) {
      suggestions.add(entry.command);
      
      if (suggestions.size >= limit) {
        break;
      }
    }
  }
  
  return Array.from(suggestions);
}
