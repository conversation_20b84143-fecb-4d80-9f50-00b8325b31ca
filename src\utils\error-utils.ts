/**
 * Error handling utilities
 * 
 * Provides safe error handling for unknown error types
 */

/**
 * Safely convert unknown error to Error object
 */
export function toError(error: unknown): Error {
  if (error instanceof Error) {
    return error;
  }
  
  if (typeof error === 'string') {
    return new Error(error);
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return new Error(String(error.message));
  }
  
  return new Error('Unknown error occurred');
}

/**
 * Safely get error message from unknown error
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  
  return 'Unknown error occurred';
}

/**
 * Safely get error status code from unknown error
 */
export function getErrorStatus(error: unknown): number | undefined {
  if (error && typeof error === 'object' && 'status' in error) {
    const status = (error as any).status;
    return typeof status === 'number' ? status : undefined;
  }
  
  return undefined;
}

/**
 * Check if error has a specific property
 */
export function hasErrorProperty(error: unknown, property: string): boolean {
  return error && typeof error === 'object' && property in error;
}

/**
 * Safely get error property value
 */
export function getErrorProperty(error: unknown, property: string): unknown {
  if (hasErrorProperty(error, property)) {
    return (error as any)[property];
  }
  
  return undefined;
}

/**
 * Create a standardized error object with additional context
 */
export function createError(message: string, originalError?: unknown, context?: Record<string, unknown>): Error {
  const error = new Error(message);
  
  if (originalError) {
    (error as any).originalError = originalError;
    (error as any).originalMessage = getErrorMessage(originalError);
  }
  
  if (context) {
    Object.assign(error, context);
  }
  
  return error;
}

/**
 * Wrap async function with error handling
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  errorHandler?: (error: unknown) => void
): (...args: T) => Promise<R | undefined> {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      }
      return undefined;
    }
  };
}

/**
 * Wrap sync function with error handling
 */
export function withSyncErrorHandling<T extends any[], R>(
  fn: (...args: T) => R,
  errorHandler?: (error: unknown) => void
): (...args: T) => R | undefined {
  return (...args: T): R | undefined => {
    try {
      return fn(...args);
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      }
      return undefined;
    }
  };
}
