/**
 * Custom Spinner Component
 *
 * Provides animated spinner widgets with different animation types
 * and configurable speed for loading indicators.
 */
import blessed from 'blessed';
import type { BlessedStyle } from '../../types/blessed-extensions.js';
export interface SpinnerOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    type?: 'dots' | 'ball' | 'line' | 'bounce';
    speed?: number;
    style?: BlessedStyle;
    hidden?: boolean;
}
declare const spinnerTypes: Record<string, string[]>;
export declare class Spinner {
    private element;
    private frames;
    private currentFrame;
    private interval;
    private speed;
    private isSpinning;
    constructor(options: SpinnerOptions);
    /**
     * Start spinning animation
     */
    start(): void;
    /**
     * Stop spinning animation
     */
    stop(): void;
    /**
     * Set spinner text
     */
    setText(text: string): void;
    /**
     * Change spinner type
     */
    setType(type: keyof typeof spinnerTypes): void;
    /**
     * Change spinner speed
     */
    setSpeed(speed: number): void;
    /**
     * Get the underlying blessed element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Check if spinner is currently spinning
     */
    isActive(): boolean;
    /**
     * Destroy the spinner
     */
    destroy(): void;
    /**
     * Show the spinner element
     */
    show(): void;
    /**
     * Hide the spinner element
     */
    hide(): void;
    /**
     * Set position
     */
    setPosition(top: string | number, left: string | number): void;
    /**
     * Set size
     */
    setSize(width: string | number, height: string | number): void;
}
/**
 * Create a spinner with text
 */
export declare function createSpinnerWithText(parent: blessed.Widgets.Node, text: string, options?: Partial<SpinnerOptions>): Spinner;
/**
 * Create a loading spinner
 */
export declare function createLoadingSpinner(parent: blessed.Widgets.Node, position?: {
    top: string | number;
    left: string | number;
}): Spinner;
export default Spinner;
//# sourceMappingURL=spinner.d.ts.map