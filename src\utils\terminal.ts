/**
 * Terminal Management Utilities
 * 
 * Handles terminal state management, screen clearing, cursor control,
 * and integration with the blessed.js renderer.
 */

import blessed from 'blessed';
import { logDebug, logError, fpsDebugger } from './logger/log.js';
import { toError } from './error-utils.js';

let currentRenderer: blessed.Widgets.Screen | null = null;
let isTerminalSetup = false;
let originalTerminalState: any = null;

/**
 * Set the current blessed renderer
 */
export function setRenderer(renderer: blessed.Widgets.Screen): void {
  currentRenderer = renderer;
  setupTerminal();
  logDebug('Terminal renderer set');
}

/**
 * Get the current renderer
 */
export function getRenderer(): blessed.Widgets.Screen | null {
  return currentRenderer;
}

/**
 * Setup terminal for blessed.js usage
 */
function setupTerminal(): void {
  if (isTerminalSetup) {
    return;
  }

  try {
    // Save original terminal state
    originalTerminalState = {
      isTTY: process.stdout.isTTY,
      columns: process.stdout.columns,
      rows: process.stdout.rows,
    };

    // Setup terminal for blessed
    if (process.stdout.isTTY) {
      // Enable mouse support if available
      process.stdout.write('\x1b[?1000h'); // Mouse tracking
      process.stdout.write('\x1b[?1002h'); // Cell motion tracking
      process.stdout.write('\x1b[?1015h'); // Extended coordinates
      process.stdout.write('\x1b[?1006h'); // SGR coordinates
      
      // Enable alternative screen buffer
      process.stdout.write('\x1b[?1049h');
      
      // Hide cursor initially
      process.stdout.write('\x1b[?25l');
    }

    isTerminalSetup = true;
    logDebug('Terminal setup completed');

  } catch (error) {
    logError('Failed to setup terminal', toError(error));
  }
}

/**
 * Restore terminal to original state
 */
export function restoreTerminal(): void {
  if (!isTerminalSetup) {
    return;
  }

  try {
    if (process.stdout.isTTY) {
      // Show cursor
      process.stdout.write('\x1b[?25h');
      
      // Disable alternative screen buffer
      process.stdout.write('\x1b[?1049l');
      
      // Disable mouse support
      process.stdout.write('\x1b[?1006l');
      process.stdout.write('\x1b[?1015l');
      process.stdout.write('\x1b[?1002l');
      process.stdout.write('\x1b[?1000l');
      
      // Reset colors and styles
      process.stdout.write('\x1b[0m');
    }

    isTerminalSetup = false;
    logDebug('Terminal restored');

  } catch (error) {
    logError('Failed to restore terminal', toError(error));
  }
}

/**
 * Clear terminal screen and scrollback
 */
export function clearTerminal(): void {
  try {
    if (currentRenderer) {
      // Clear blessed screen
      (currentRenderer as any).clearRegion(0, (currentRenderer as any).width, 0, (currentRenderer as any).height);
      currentRenderer.render();
    } else {
      // Fallback to ANSI escape sequences
      if (process.stdout.isTTY) {
        // Clear screen and scrollback
        process.stdout.write('\x1b[2J\x1b[3J\x1b[H');
      }
    }
    
    logDebug('Terminal cleared');

  } catch (error) {
    logError('Failed to clear terminal', toError(error));
  }
}

/**
 * Show cursor
 */
export function showCursor(): void {
  try {
    if (process.stdout.isTTY) {
      process.stdout.write('\x1b[?25h');
    }
  } catch (error) {
    logError('Failed to show cursor', toError(error));
  }
}

/**
 * Hide cursor
 */
export function hideCursor(): void {
  try {
    if (process.stdout.isTTY) {
      process.stdout.write('\x1b[?25l');
    }
  } catch (error) {
    logError('Failed to hide cursor', toError(error));
  }
}

/**
 * Move cursor to position
 */
export function moveCursor(x: number, y: number): void {
  try {
    if (process.stdout.isTTY) {
      process.stdout.write(`\x1b[${y + 1};${x + 1}H`);
    }
  } catch (error) {
    logError('Failed to move cursor', toError(error));
  }
}

/**
 * Get terminal size
 */
export function getTerminalSize(): { width: number; height: number } {
  return {
    width: process.stdout.columns || 80,
    height: process.stdout.rows || 24,
  };
}

/**
 * Check if terminal supports colors
 */
export function supportsColor(): boolean {
  return process.stdout.isTTY && (
    process.env.COLORTERM === 'truecolor' ||
    process.env.TERM === 'xterm-256color' ||
    process.env.TERM?.includes('color') ||
    false
  );
}

/**
 * Check if terminal supports Unicode
 */
export function supportsUnicode(): boolean {
  const lang = process.env.LANG || process.env.LC_ALL || '';
  return lang.toLowerCase().includes('utf') || lang.toLowerCase().includes('unicode');
}

/**
 * Enable raw mode for input
 */
export function enableRawMode(): void {
  try {
    if (process.stdin.isTTY && process.stdin.setRawMode) {
      process.stdin.setRawMode(true);
      logDebug('Raw mode enabled');
    }
  } catch (error) {
    logError('Failed to enable raw mode', toError(error));
  }
}

/**
 * Disable raw mode for input
 */
export function disableRawMode(): void {
  try {
    if (process.stdin.isTTY && process.stdin.setRawMode) {
      process.stdin.setRawMode(false);
      logDebug('Raw mode disabled');
    }
  } catch (error) {
    logError('Failed to disable raw mode', toError(error));
  }
}

/**
 * Start FPS debugging for UI performance
 */
export function startFPSDebugging(): void {
  if (!currentRenderer) {
    return;
  }

  let frameCount = 0;
  let lastTime = Date.now();

  const updateFPS = () => {
    frameCount++;
    const now = Date.now();
    
    if (now - lastTime >= 1000) {
      const fps = Math.round((frameCount * 1000) / (now - lastTime));
      (fpsDebugger as any).setFPS?.(fps);
      frameCount = 0;
      lastTime = now;
    }
  };

  // Hook into render cycle
  const originalRender = currentRenderer.render.bind(currentRenderer);
  currentRenderer.render = function() {
    updateFPS();
    return originalRender();
  };

  logDebug('FPS debugging started');
}

/**
 * Handle terminal resize events
 */
export function onTerminalResize(callback: (width: number, height: number) => void): () => void {
  const handler = () => {
    const { width, height } = getTerminalSize();
    callback(width, height);
  };

  process.stdout.on('resize', handler);
  
  // Return cleanup function
  return () => {
    process.stdout.off('resize', handler);
  };
}

/**
 * Setup graceful exit handling
 */
export function onExit(): void {
  const cleanup = () => {
    try {
      restoreTerminal();
      showCursor();
      
      if (currentRenderer) {
        currentRenderer.destroy();
      }
      
    } catch (error) {
      // Ignore cleanup errors during exit
    }
  };

  // Handle various exit signals
  process.on('exit', cleanup);
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('SIGQUIT', cleanup);
  
  // Handle Windows signals
  if (process.platform === 'win32') {
    process.on('SIGBREAK', cleanup);
  }

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logError('Uncaught exception during terminal operation', error);
    cleanup();
    process.exit(1);
  });

  process.on('unhandledRejection', (reason) => {
    logError('Unhandled rejection during terminal operation', reason as Error);
    cleanup();
    process.exit(1);
  });
}

/**
 * Initialize terminal management
 */
export function initializeTerminal(): void {
  onExit();
  logDebug('Terminal management initialized');
}

/**
 * Check if terminal is interactive
 */
export function isInteractive(): boolean {
  return process.stdout.isTTY && process.stdin.isTTY;
}

/**
 * Get terminal capabilities
 */
export function getTerminalCapabilities(): {
  colors: boolean;
  unicode: boolean;
  mouse: boolean;
  interactive: boolean;
  size: { width: number; height: number };
} {
  return {
    colors: supportsColor(),
    unicode: supportsUnicode(),
    mouse: process.stdout.isTTY,
    interactive: isInteractive(),
    size: getTerminalSize(),
  };
}

// Initialize on module load
initializeTerminal();
